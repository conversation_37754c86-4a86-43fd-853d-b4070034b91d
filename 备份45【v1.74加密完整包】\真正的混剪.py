import os
import random
import subprocess
from pathlib import Path
import time
import shutil

# 设置 FFmpeg 路径
FFMPEG_PATH = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
FFPROBE_PATH = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe")

def get_video_duration(video_path):
    """获取视频的时长（秒）"""
    try:
        cmd = [
            str(FFPROBE_PATH),
            "-v", "error",
            "-show_entries", "format=duration",
            "-of", "default=noprint_wrappers=1:nokey=1",
            str(video_path)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return float(result.stdout.strip())
    except Exception as e:
        print(f"获取视频时长失败: {video_path}, 错误: {e}")
        return 0

def is_valid_audio_file(audio_path):
    """检查音频文件是否有效"""
    try:
        cmd = [
            str(FFPROBE_PATH),
            "-v", "error",
            "-select_streams", "a",
            "-show_entries", "stream=codec_type",
            "-of", "default=noprint_wrappers=1:nokey=1",
            str(audio_path)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return 'audio' in result.stdout.lower()
    except Exception as e:
        print(f"检查音频文件失败: {audio_path}, 错误: {e}")
        return False

def select_video_clips(video_folder, target_duration):
    """从视频文件夹中选择多个视频片段，总时长接近目标时长，且不重复使用素材"""
    video_files = [f for f in os.listdir(video_folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
    if not video_files:
        raise ValueError("混剪素材文件夹中没有找到视频文件")
    
    # 打乱视频文件顺序，确保随机性
    random.shuffle(video_files)
    
    selected_clips = []
    current_duration = 0
    used_files = set()
    
    for video_file in video_files:
        # 避免重复使用素材
        if video_file in used_files:
            continue
            
        video_path = os.path.join(video_folder, video_file)
        
        # 获取视频时长
        duration = get_video_duration(video_path)
        if duration <= 3:  # 视频太短，跳过
            continue
        
        # 随机选择片段长度（3-5秒）
        clip_duration = round(random.uniform(3, 5), 2)
        
        # 如果剩余时长不足，调整片段长度
        if current_duration + clip_duration > target_duration:
            clip_duration = target_duration - current_duration
            if clip_duration < 3:  # 片段太短，跳过
                continue
        
        # 随机选择开始时间点
        max_start_time = max(0, duration - clip_duration)
        start_time = round(random.uniform(0, max_start_time), 2)
        
        # 添加到选择的片段列表
        selected_clips.append({
            'file': video_path,
            'start_time': start_time,
            'duration': clip_duration
        })
        
        used_files.add(video_file)
        current_duration += clip_duration
        
        # 如果达到目标时长，停止选择
        if current_duration >= target_duration:
            break
    
    return selected_clips

def select_end_video(end_folder):
    """从结尾文件夹中随机选择一个视频"""
    end_files = [f for f in os.listdir(end_folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
    if not end_files:
        raise ValueError("结尾文件夹中没有找到视频文件")
    
    end_file = random.choice(end_files)
    end_path = os.path.join(end_folder, end_file)
    
    # 确保结尾视频有效
    duration = get_video_duration(end_path)
    if duration <= 0:
        raise ValueError(f"无效的结尾视频: {end_file}")
    
    return end_path

def select_music(music_folder):
    """从音乐文件夹中随机选择一首音乐，确保音频文件有效"""
    music_files = [f for f in os.listdir(music_folder) if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))]
    if not music_files:
        raise ValueError("音乐文件夹中没有找到音频文件")
    
    # 尝试找到一个有效的音频文件
    valid_music_files = []
    for music_file in music_files:
        music_path = os.path.join(music_folder, music_file)
        if is_valid_audio_file(music_path):
            valid_music_files.append(music_path)
    
    if not valid_music_files:
        raise ValueError("音乐文件夹中没有找到有效的音频文件")
    
    return random.choice(valid_music_files)

def create_video_montage(output_file, video_clips, end_video, music_file, total_duration):
    """创建混剪视频"""
    # 创建临时目录
    temp_dir = f"temp_{int(time.time())}"
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # 1. 生成视频片段列表文件
        clips_list_file = os.path.join(temp_dir, "clips_list.txt")
        with open(clips_list_file, 'w', encoding='utf-8') as f:
            for i, clip in enumerate(video_clips):
                clip_output = os.path.join(temp_dir, f"clip_{i}.mp4")
                
                # 提取视频片段
                cmd = [
                    str(FFMPEG_PATH),
                    "-ss", str(clip['start_time']),
                    "-t", str(clip['duration']),
                    "-i", clip['file'],
                    "-c:v", "libx264",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-loglevel", "error",
                    clip_output
                ]
                subprocess.run(cmd, check=True)
                
                # 将片段添加到列表文件
                f.write(f"file '{os.path.abspath(clip_output)}'\n")
        
        # 2. 合并所有视频片段
        merged_clips_file = os.path.join(temp_dir, "merged_clips.mp4")
        cmd = [
            str(FFMPEG_PATH),
            "-f", "concat",
            "-safe", "0",
            "-i", clips_list_file,
            "-c", "copy",
            "-loglevel", "error",
            merged_clips_file
        ]
        subprocess.run(cmd, check=True)
        
        # 3. 添加结尾视频 - 使用更安全的方法处理不同帧率的视频
        final_video_no_audio = os.path.join(temp_dir, "final_video_no_audio.mp4")
        
        # 获取第一个视频的帧率，以便在合并时保持一致
        first_video = video_clips[0]['file']
        cmd = [
            str(FFPROBE_PATH),
            "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=r_frame_rate",
            "-of", "default=noprint_wrappers=1:nokey=1",
            first_video
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        frame_rate = result.stdout.strip()
        
        # 重新编码所有视频片段以确保帧率一致
        reencoded_clips = []
        for i, clip in enumerate(video_clips):
            reencoded_clip = os.path.join(temp_dir, f"reencoded_clip_{i}.mp4")
            cmd = [
                str(FFMPEG_PATH),
                "-i", os.path.join(temp_dir, f"clip_{i}.mp4"),
                "-r", frame_rate,
                "-c:v", "libx264",
                "-c:a", "aac",
                "-b:a", "192k",
                "-loglevel", "error",
                reencoded_clip
            ]
            subprocess.run(cmd, check=True)
            reencoded_clips.append(reencoded_clip)
        
        # 重新编码结尾视频以匹配帧率
        reencoded_end_video = os.path.join(temp_dir, "reencoded_end_video.mp4")
        cmd = [
            str(FFMPEG_PATH),
            "-i", end_video,
            "-r", frame_rate,
            "-c:v", "libx264",
            "-c:a", "aac",
            "-b:a", "192k",
            "-loglevel", "error",
            reencoded_end_video
        ]
        subprocess.run(cmd, check=True)
        
        # 创建新的列表文件
        final_list_file = os.path.join(temp_dir, "final_list.txt")
        with open(final_list_file, 'w', encoding='utf-8') as f:
            for clip in reencoded_clips:
                f.write(f"file '{os.path.abspath(clip)}'\n")
            f.write(f"file '{os.path.abspath(reencoded_end_video)}'\n")
        
        # 合并所有视频
        cmd = [
            str(FFMPEG_PATH),
            "-f", "concat",
            "-safe", "0",
            "-i", final_list_file,
            "-c", "copy",
            "-loglevel", "error",
            final_video_no_audio
        ]
        subprocess.run(cmd, check=True)
        
        # 4. 处理背景音乐 - 调整为-10dB
        music_output = os.path.join(temp_dir, "bg_music.aac")
        
        # 调整音乐长度以匹配视频总时长，明确指定输出格式
        cmd = [
            str(FFMPEG_PATH),
            "-i", music_file,
            "-filter_complex", f"aloop=loop=-1:size=2e+09,alimiter=limit=0.99,volume=-10dB,atrim=0:{total_duration}",
            "-t", str(total_duration),
            "-c:a", "aac",
            "-b:a", "192k",
            "-f", "adts",
            "-loglevel", "error",
            music_output
        ]
        subprocess.run(cmd, check=True)
        
        # 5. 合并视频和音乐，同时使用loudnorm滤镜统一响度
        cmd = [
            str(FFMPEG_PATH),
            "-i", final_video_no_audio,
            "-i", music_output,
            "-filter_complex", f"[0:a]loudnorm=I=-16:LRA=11:TP=-1.5,volume=-5dB[a0];[1:a]loudnorm=I=-16:LRA=11:TP=-1.5[a1];[a1][a0]amix=inputs=2:duration=first:dropout_transition=3[aout]",
            "-map", "0:v",
            "-map", "[aout]",
            "-c:v", "copy",
            "-shortest",
            "-loglevel", "error",
            output_file
        ]
        subprocess.run(cmd, check=True)
        
        print(f"已成功生成混剪视频: {output_file}")
        
    except Exception as e:
        print(f"生成视频时出错: {e}")
        # 保留临时文件以便调试
        print(f"临时文件保存在: {temp_dir}")
    else:
        # 只有在没有错误的情况下才清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"清理临时文件时出错: {e}")

def main():
    # 获取用户输入的输出视频数量
    try:
        num_videos = int(input("请输入要生成的视频数量: "))
        if num_videos <= 0:
            print("请输入一个正整数!")
            return
    except ValueError:
        print("输入无效，请输入一个整数!")
        return
    
    # 确保FFmpeg和FFprobe可用
    if not FFMPEG_PATH.exists():
        print(f"错误: FFmpeg可执行文件不存在于路径 {FFMPEG_PATH}")
        return
    
    if not FFPROBE_PATH.exists():
        print(f"错误: FFprobe可执行文件不存在于路径 {FFPROBE_PATH}")
        return
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置素材文件夹路径
    video_folder = os.path.join(current_dir, "混剪素材")
    end_folder = os.path.join(current_dir, "结尾")
    music_folder = os.path.join(current_dir, "音乐")
    
    # 检查文件夹是否存在
    for folder in [video_folder, end_folder, music_folder]:
        if not os.path.exists(folder):
            print(f"错误: 文件夹 {folder} 不存在")
            return
    
    # 创建输出文件夹
    output_folder = os.path.join(current_dir, "输出视频")
    os.makedirs(output_folder, exist_ok=True)
    
    # 生成指定数量的混剪视频
    for i in range(num_videos):
        print(f"\n正在生成第 {i+1}/{num_videos} 个混剪视频...")
        
        try:
            # 随机确定目标视频总时长（15-31秒）
            target_duration = round(random.uniform(15, 31), 2)
            print(f"目标视频总时长: {target_duration} 秒")
            
            # 选择视频片段（总时长约为目标时长减去结尾视频时长）
            end_video = select_end_video(end_folder)
            end_duration = get_video_duration(end_video)
            if end_duration <= 0:
                print("无法获取结尾视频时长，跳过这个视频")
                continue
            
            clips_duration = max(5, target_duration - end_duration)  # 至少保留5秒给视频片段
            video_clips = select_video_clips(video_folder, clips_duration)
            
            # 检查是否选择了足够的视频片段
            if not video_clips:
                print("无法选择足够的视频片段，跳过这个视频")
                continue
            
            # 计算实际视频片段总时长
            actual_clips_duration = sum(clip['duration'] for clip in video_clips)
            total_video_duration = actual_clips_duration + end_duration
            print(f"实际视频片段总时长: {actual_clips_duration:.2f} 秒")
            print(f"结尾视频时长: {end_duration:.2f} 秒")
            print(f"最终视频总时长: {total_video_duration:.2f} 秒")
            
            # 选择背景音乐，确保它是有效的
            music_file = select_music(music_folder)
            print(f"选择的背景音乐: {os.path.basename(music_file)}")
            
            # 创建输出文件名
            output_file = os.path.join(output_folder, f"混剪视频_{i+1}.mp4")
            
            # 创建混剪视频
            create_video_montage(output_file, video_clips, end_video, music_file, total_video_duration)
            
        except Exception as e:
            print(f"生成视频时出错: {e}")
            continue
    
    print("\n所有混剪视频已生成完成!")

if __name__ == "__main__":
    main()    