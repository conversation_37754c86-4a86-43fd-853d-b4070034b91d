#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间验证模块
实现授权时间限制和防篡改功能
"""

import json
import time
import hashlib
import hmac
import random
import os
import sys
from datetime import datetime
try:
    from cryptography.fernet import Fernet
    import base64
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 注意：此版本已移除网络时间验证，改为纯本地时间验证

class TimeValidator:
    def __init__(self):
        # 基本属性
        self.secret_key = "MFChen_Time_Validator_2024_Secret"
        self.max_time_rollback = 12 * 3600  # 12小时，超过此值认为时间被篡改
        self.max_history_days = 7  # 历史记录有效期
        self.min_time_interval = 60  # 最小时间间隔（秒），防止频繁记录

        # 延迟初始化的属性
        self._cipher_key = None
        self._time_record_file = None

    def _lazy_init(self):
        """延迟初始化耗时的属性"""
        if self._cipher_key is None:
            self._cipher_key = self._generate_cipher_key()

        if self._time_record_file is None:
            self._time_record_file = os.path.join(os.path.expanduser("~"), ".mfchen_time_record.dat")

    @property
    def cipher_key(self):
        self._lazy_init()
        return self._cipher_key

    @property
    def time_record_file(self):
        self._lazy_init()
        return self._time_record_file
        
    def _generate_cipher_key(self):
        """生成加密密钥"""
        if CRYPTO_AVAILABLE:
            key_hash = hashlib.sha256(self.secret_key.encode()).digest()
            return base64.urlsafe_b64encode(key_hash)
        else:
            return self.secret_key.encode()

    def _encrypt_data(self, data):
        """加密数据"""
        try:
            json_data = json.dumps(data)
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                encrypted = cipher.encrypt(json_data.encode())
                return encrypted
            else:
                # 简单的XOR加密作为备用
                key = self.cipher_key
                encrypted = bytearray()
                for i, byte in enumerate(json_data.encode()):
                    encrypted.append(byte ^ key[i % len(key)])
                return bytes(encrypted)
        except Exception:
            return None

    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                decrypted = cipher.decrypt(encrypted_data)
                return json.loads(decrypted.decode())
            else:
                # 简单的XOR解密作为备用
                key = self.cipher_key
                decrypted = bytearray()
                for i, byte in enumerate(encrypted_data):
                    decrypted.append(byte ^ key[i % len(key)])
                return json.loads(decrypted.decode())
        except Exception:
            return None
    
    def _generate_hmac(self, timestamp, access_time, sequence):
        """生成HMAC校验值"""
        message = f"{timestamp}{access_time}{sequence}"
        return hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()[:16]
    
    def _get_local_time(self):
        """获取本地时间"""
        return int(time.time())
    
    def _load_time_history(self):
        """加载时间历史记录"""
        if not os.path.exists(self.time_record_file):
            return {"history": [], "first_use": None, "last_use": None, "access_count": 0}

        try:
            with open(self.time_record_file, 'rb') as f:
                encrypted_data = f.read()
            data = self._decrypt_data(encrypted_data) or {"history": [], "first_use": None, "last_use": None, "access_count": 0}
            # 确保有access_count字段
            if "access_count" not in data:
                data["access_count"] = 0
            return data
        except Exception:
            return {"history": [], "first_use": None, "last_use": None, "access_count": 0}
    
    def _save_time_history(self, history_data):
        """保存时间历史记录"""
        try:
            encrypted_data = self._encrypt_data(history_data)
            if encrypted_data:
                with open(self.time_record_file, 'wb') as f:
                    f.write(encrypted_data)
                return True
        except Exception:
            pass
        return False
    
    def _validate_time_record(self, record):
        """验证时间记录的完整性"""
        try:
            # 检查是否为新格式记录
            if 'timestamp' in record and 'access_time' in record:
                # 新的本地时间记录格式验证
                expected_hash = self._generate_hmac(record['timestamp'], record['access_time'], record.get('sequence', 0))
                return record.get('hash') == expected_hash
            elif 'net' in record and 'local' in record:
                # 旧格式记录，跳过验证（兼容性处理）
                return False  # 旧格式记录不再有效
            else:
                return False
        except Exception:
            return False
    
    def get_current_time_info(self):
        """获取当前时间信息（纯本地时间验证）"""
        current_time = self._get_local_time()
        history_data = self._load_time_history()

        # 检查时间回滚（如果当前时间比上次使用时间早12小时以上，认为时间被篡改）
        time_rollback_detected = False
        if history_data["last_use"] and current_time < (history_data["last_use"] - self.max_time_rollback):
            time_rollback_detected = True

        # 检查是否应该记录新的时间点
        should_record = False
        if history_data["last_use"] is None:
            # 首次使用
            should_record = True
        elif current_time - history_data["last_use"] >= self.min_time_interval:
            # 距离上次记录超过最小间隔
            should_record = True

        if should_record and not time_rollback_detected:
            # 增加访问计数
            history_data["access_count"] += 1

            # 创建新的时间记录
            new_record = {
                "timestamp": current_time,
                "access_time": current_time,
                "sequence": history_data["access_count"],
                "hash": self._generate_hmac(current_time, current_time, history_data["access_count"])
            }

            history_data["history"].append(new_record)

            # 只保留最近10条记录（增加记录数量以便更好地检测篡改）
            if len(history_data["history"]) > 10:
                history_data["history"] = history_data["history"][-10:]

            # 更新最后使用时间
            history_data["last_use"] = current_time

            # 如果是首次使用，记录首次使用时间
            if history_data["first_use"] is None:
                history_data["first_use"] = current_time

        return {
            "current_time": current_time,
            "is_network": False,
            "local_time": current_time,
            "time_rollback_detected": time_rollback_detected,
            "history_data": history_data
        }
    
    def _check_time_consistency(self, history_data, current_time):
        """检查时间一致性，检测可能的时间篡改"""
        if not history_data["history"]:
            return True, "无历史记录"

        # 只验证新格式记录的完整性
        valid_records = []
        for record in history_data["history"]:
            # 只处理新格式的记录
            if 'timestamp' in record and self._validate_time_record(record):
                # 检查记录是否在有效期内
                record_age = current_time - record["timestamp"]
                if record_age <= self.max_history_days * 24 * 3600 and record_age >= 0:
                    valid_records.append(record)

        if not valid_records:
            return True, "无有效新格式历史记录（兼容模式）"

        # 检查时间序列的连续性
        valid_records.sort(key=lambda x: x["sequence"])

        for i in range(1, len(valid_records)):
            prev_record = valid_records[i-1]
            curr_record = valid_records[i]

            # 检查时间戳是否递增
            if curr_record["timestamp"] < prev_record["timestamp"]:
                return False, "检测到时间回滚"

            # 检查序列号是否递增
            if curr_record["sequence"] <= prev_record["sequence"]:
                return False, "检测到序列异常"

        return True, "时间一致性检查通过"
    
    def validate_authorization_time(self, expire_timestamp):
        """验证授权时间（纯本地时间验证）"""
        time_info = self.get_current_time_info()
        history_data = time_info["history_data"]
        current_time = time_info["current_time"]

        # 1. 检查时间回滚（如果检测到时间被回调超过12小时，触发异常A）
        if time_info.get("time_rollback_detected", False):
            return self._generate_error_code("A")

        # 2. 检查时间一致性
        consistency_ok, _ = self._check_time_consistency(history_data, current_time)
        if not consistency_ok:
            # 时间一致性检查失败，可能存在篡改
            return self._generate_error_code("A")

        # 3. 检查是否为首次使用
        if history_data["first_use"] is None:
            # 首次使用，记录首次使用时间
            history_data["first_use"] = current_time
            history_data["last_use"] = current_time
            self._save_time_history(history_data)

            # 使用本地时间验证授权是否过期
            if current_time > expire_timestamp:
                return self._generate_error_code("A")
            return True, "首次使用授权验证通过（本地时间验证）"

        # 4. 检查授权是否过期
        if current_time > expire_timestamp:
            return self._generate_error_code("A")

        # 5. 检查使用频率是否异常（防止快速修改时间多次使用）
        if len(history_data["history"]) > 1:
            # 只检查新格式的记录
            new_format_records = [r for r in history_data["history"] if 'timestamp' in r]
            if len(new_format_records) > 1:
                recent_records = new_format_records[-5:]  # 检查最近5次记录
                time_intervals = []
                for i in range(1, len(recent_records)):
                    interval = recent_records[i]["timestamp"] - recent_records[i-1]["timestamp"]
                    time_intervals.append(interval)

                # 如果最近的时间间隔都很短（小于30秒），可能存在异常
                if time_intervals and all(interval < 30 for interval in time_intervals):
                    return self._generate_error_code("A")

        # 保存历史数据
        self._save_time_history(history_data)

        return True, "授权时间验证通过（本地时间验证）"
    
    def _generate_error_code(self, error_type):
        """生成错误代码"""
        random_num = random.randint(100, 999)
        error_code = f"{random_num}{error_type}"
        return False, f"授权异常 {error_code}，请重新授权"

# 测试函数
def test_time_validator():
    """测试时间验证器"""
    validator = TimeValidator()
    
    print("测试时间验证器...")
    time_info = validator.get_current_time_info()
    print(f"当前时间信息: {time_info}")
    
    # 测试未来时间（应该通过）
    future_time = int(time.time()) + 30 * 24 * 3600  # 30天后
    result = validator.validate_authorization_time(future_time)
    print(f"未来时间验证: {result}")
    
    # 测试过去时间（应该失败）
    past_time = int(time.time()) - 24 * 3600  # 1天前
    result = validator.validate_authorization_time(past_time)
    print(f"过去时间验证: {result}")

if __name__ == "__main__":
    test_time_validator()
