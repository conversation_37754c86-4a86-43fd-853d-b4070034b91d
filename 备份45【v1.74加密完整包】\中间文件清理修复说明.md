# 中间文件清理修复说明

## 问题描述

在开启扩展功能时，中间文件没有删除干净，特别是最开始的混剪文件（如 `merged_1_1_1.mp4`）仍然残留在输出目录中。

## 问题原因分析

### 1. 文件处理流程
```
原始混剪文件: merged_1_1_1.mp4
↓ 抽帧处理
抽帧后文件: merged_1_1_1_processed.mp4
↓ 背景音乐处理
音乐后文件: merged_1_1_1_processed_with_music.mp4
↓ 批量重命名
最终文件: DEF5061301-吉.mp4
```

### 2. 问题所在
- 每个扩展功能处理后，都会生成新文件
- 但旧的中间文件（如 `merged_1_1_1.mp4`、`merged_1_1_1_processed.mp4`）没有被标记为待删除
- 最终清理时，只保留了最终文件，但中间文件的清理逻辑不完整

## 修复方案

### 1. 添加中间文件标记机制

在 `ExtensionThread` 类中添加了文件标记功能：

```python
def _mark_file_for_deletion(self, file):
    """标记文件为待删除的中间文件"""
    if not hasattr(self, '_files_to_delete'):
        self._files_to_delete = set()
    self._files_to_delete.add(file)
    
def get_files_to_delete(self):
    """获取待删除的文件列表"""
    return getattr(self, '_files_to_delete', set())
```

### 2. 修改扩展功能处理逻辑

#### 抽帧处理修改
```python
def _safe_frame_removal(self, file):
    # ... 处理逻辑 ...
    
    if os.path.exists(output_path):
        # 如果输出文件已存在，标记输入文件为待删除
        self._mark_file_for_deletion(file)
        return output_path
    
    # ... FFmpeg处理 ...
    
    if result.returncode == 0:
        # 处理成功，标记输入文件为待删除
        self._mark_file_for_deletion(file)
        return output_path
```

#### 背景音乐处理修改
```python
def _safe_background_music(self, file):
    # ... 处理逻辑 ...
    
    if os.path.exists(new_file_path):
        # 如果输出文件已存在，标记输入文件为待删除
        self._mark_file_for_deletion(file)
        return new_file_path
```

### 3. 扩展功能完成后立即清理

修改 `on_extension_finished` 方法：

```python
def on_extension_finished(self, processed_files, file_tracker):
    """扩展功能完成回调"""
    self.file_tracker = file_tracker
    
    # 获取扩展功能线程中标记的待删除文件
    if hasattr(self.extension_thread, '_files_to_delete'):
        files_to_delete = self.extension_thread.get_files_to_delete()
        self.update_log(f"🗑️ 准备清理 {len(files_to_delete)} 个中间文件")
        
        # 立即删除中间文件
        deleted_count = 0
        for file_to_delete in files_to_delete:
            try:
                if os.path.exists(file_to_delete):
                    os.remove(file_to_delete)
                    self.update_log(f"✅ 删除中间文件：{os.path.basename(file_to_delete)}")
                    deleted_count += 1
            except Exception as e:
                self.update_log(f"❌ 删除中间文件失败：{os.path.basename(file_to_delete)}")
        
        if deleted_count > 0:
            self.update_log(f"🧹 已清理 {deleted_count} 个中间文件")
    
    self.on_all_tasks_finished(processed_files)
```

## 修复效果

### 修复前的文件残留
```
输出目录中的文件：
- merged_1_1_1.mp4                    ← 残留的混剪文件
- merged_1_1_1_processed.mp4          ← 残留的抽帧文件
- merged_1_1_1_processed_with_music.mp4 ← 残留的音乐文件
- DEF5061301-吉.mp4                   ← 最终文件
```

### 修复后的文件清理
```
输出目录中的文件：
- DEF5061301-吉.mp4                   ← 只保留最终文件
- 原始文件.mp4                        ← 用户原有的文件（如果有）
```

## 清理逻辑

### 1. 实时标记
- 每个扩展功能处理时，将输入文件标记为待删除
- 使用 `set` 数据结构避免重复标记

### 2. 批量清理
- 扩展功能全部完成后，统一删除标记的中间文件
- 提供详细的清理日志

### 3. 安全保护
- 只删除明确标记的中间文件
- 保留用户原有的文件和最终处理结果

## 日志输出示例

```
🗑️ 准备清理 3 个中间文件
✅ 删除中间文件：merged_1_1_1.mp4
✅ 删除中间文件：merged_1_1_1_processed.mp4
✅ 删除中间文件：merged_1_1_1_processed_with_music.mp4
🧹 已清理 3 个中间文件
```

## 技术特点

### 1. 线程安全
- 中间文件标记在扩展功能线程中进行
- 文件删除在主线程中执行，确保UI响应

### 2. 精确清理
- 只删除处理过程中产生的中间文件
- 不影响用户原有文件和最终结果

### 3. 错误处理
- 删除失败时记录错误日志
- 不会因为单个文件删除失败而影响整体流程

## 使用效果

现在当您使用扩展功能时：
1. ✅ 混剪文件会被正确处理
2. ✅ 中间文件会被自动标记和清理
3. ✅ 输出目录只保留最终结果和原有文件
4. ✅ 节省磁盘空间，避免文件混乱

这个修复确保了扩展功能处理过程中产生的所有中间文件都能被正确清理，让输出目录保持整洁。
