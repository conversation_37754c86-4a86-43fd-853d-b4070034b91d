#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间回滚检测功能
"""

import os
import time
from time_validator import TimeValidator

def test_time_rollback_detection():
    """测试时间回滚检测功能"""
    print("=" * 50)
    print("测试时间回滚检测功能")
    print("=" * 50)
    
    # 创建时间验证器
    validator = TimeValidator()
    
    # 清理旧的测试数据
    if os.path.exists(validator.time_record_file):
        os.remove(validator.time_record_file)
        print("已清理旧的测试数据")
    
    print("\n1. 首次使用测试")
    time_info = validator.get_current_time_info()
    print(f"首次使用时间信息: {time_info['current_time']}")
    
    # 测试正常授权验证
    future_time = int(time.time()) + 30 * 24 * 3600  # 30天后
    result = validator.validate_authorization_time(future_time)
    print(f"首次授权验证结果: {result}")
    
    print("\n2. 正常使用测试（等待2秒）")
    time.sleep(2)
    time_info2 = validator.get_current_time_info()
    print(f"第二次使用时间信息: {time_info2['current_time']}")
    result2 = validator.validate_authorization_time(future_time)
    print(f"第二次授权验证结果: {result2}")
    
    print("\n3. 模拟时间回滚测试")
    # 手动修改历史记录中的last_use时间，模拟时间回滚
    history_data = validator._load_time_history()
    current_time = int(time.time())
    
    # 将last_use设置为未来时间，这样当前时间就会被认为是回滚了
    history_data["last_use"] = current_time + 13 * 3600  # 比当前时间晚13小时
    validator._save_time_history(history_data)
    
    print(f"模拟设置last_use为: {history_data['last_use']}")
    print(f"当前时间为: {current_time}")
    print(f"时间差: {current_time - history_data['last_use']} 秒 ({(current_time - history_data['last_use'])/3600:.1f} 小时)")
    
    # 现在测试时间回滚检测
    time_info3 = validator.get_current_time_info()
    print(f"时间回滚检测结果: {time_info3.get('time_rollback_detected', False)}")
    
    result3 = validator.validate_authorization_time(future_time)
    print(f"时间回滚后的授权验证结果: {result3}")
    
    # 检查是否正确返回异常A
    if not result3[0] and "A" in result3[1]:
        print("✅ 时间回滚检测功能正常工作！")
    else:
        print("❌ 时间回滚检测功能可能存在问题")
    
    print("\n4. 清理测试数据")
    if os.path.exists(validator.time_record_file):
        os.remove(validator.time_record_file)
        print("测试数据已清理")

if __name__ == "__main__":
    test_time_rollback_detection()
