#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试机器码生成功能
"""

import uuid
import hashlib
import os
import platform
import subprocess

def get_machine_id():
    """生成基于多种硬件信息的稳定唯一机器码"""
    machine_info = []
    
    print("正在收集机器信息...")
    
    try:
        # 获取主板序列号
        if os.name == "nt":  # Windows
            print("检测到Windows系统，获取硬件信息...")
            try:
                result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and 'SerialNumber' not in line:
                            machine_info.append(line.strip())
                            print(f"主板序列号: {line.strip()}")
            except Exception as e:
                print(f"获取主板信息失败: {e}")
            
            # 获取CPU序列号
            try:
                result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and 'ProcessorId' not in line:
                            machine_info.append(line.strip())
                            print(f"CPU序列号: {line.strip()}")
            except Exception as e:
                print(f"获取CPU信息失败: {e}")
                
            # 获取硬盘序列号
            try:
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip() and 'SerialNumber' not in line and line.strip() != '(null)':
                            machine_info.append(line.strip())
                            print(f"硬盘序列号: {line.strip()}")
                            break  # 只取第一个硬盘
            except Exception as e:
                print(f"获取硬盘信息失败: {e}")
                
        elif os.name == "posix":  # Linux/Mac
            print("检测到Unix系统，获取硬件信息...")
            try:
                # 尝试获取机器ID
                if os.path.exists('/etc/machine-id'):
                    with open('/etc/machine-id', 'r') as f:
                        machine_id = f.read().strip()
                        machine_info.append(machine_id)
                        print(f"机器ID: {machine_id}")
                elif os.path.exists('/var/lib/dbus/machine-id'):
                    with open('/var/lib/dbus/machine-id', 'r') as f:
                        machine_id = f.read().strip()
                        machine_info.append(machine_id)
                        print(f"机器ID: {machine_id}")
            except Exception as e:
                print(f"获取机器ID失败: {e}")
                
            # 获取CPU信息
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'serial' in line.lower():
                            cpu_serial = line.split(':')[1].strip()
                            machine_info.append(cpu_serial)
                            print(f"CPU序列号: {cpu_serial}")
                            break
            except Exception as e:
                print(f"获取CPU信息失败: {e}")
    except Exception as e:
        print(f"获取硬件信息时出错: {e}")
    
    # 获取MAC地址作为备用
    try:
        mac = uuid.getnode()
        machine_info.append(str(mac))
        print(f"MAC地址: {mac}")
    except Exception as e:
        print(f"获取MAC地址失败: {e}")
    
    # 获取系统信息作为额外标识
    try:
        machine_arch = platform.machine()
        processor = platform.processor()
        machine_info.append(machine_arch)
        machine_info.append(processor)
        print(f"系统架构: {machine_arch}")
        print(f"处理器: {processor}")
    except Exception as e:
        print(f"获取系统信息失败: {e}")
    
    # 如果没有获取到任何信息，使用UUID作为备用
    if not machine_info:
        fallback_uuid = str(uuid.uuid4())
        machine_info.append(fallback_uuid)
        print(f"使用备用UUID: {fallback_uuid}")
    
    print(f"\n收集到的信息数量: {len(machine_info)}")
    print("原始信息列表:")
    for i, info in enumerate(machine_info):
        print(f"  {i+1}. {info}")
    
    # 组合所有信息并生成哈希
    combined_info = ''.join(machine_info)
    print(f"\n组合信息: {combined_info}")
    
    machine_code = hashlib.sha256(combined_info.encode()).hexdigest()[:32]
    print(f"\n生成的机器码: {machine_code}")
    
    return machine_code

def main():
    print("=" * 50)
    print("机器码生成测试")
    print("=" * 50)
    
    try:
        machine_code = get_machine_id()
        
        print("\n" + "=" * 50)
        print("测试结果")
        print("=" * 50)
        print(f"当前机器的唯一机器码: {machine_code}")
        print("\n请将此机器码添加到授权列表中:")
        print(f'"{machine_code}",')
        
        # 保存到文件
        with open("machine_code.txt", "w") as f:
            f.write(machine_code)
        print(f"\n机器码已保存到 machine_code.txt 文件中")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
