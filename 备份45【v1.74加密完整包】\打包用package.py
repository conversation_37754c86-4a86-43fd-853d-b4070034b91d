import subprocess

def package_project():
    try:
        # 执行 PyInstaller 命令
        command = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--icon=icon.ico',
            '--hidden-import=utils',  # 手动指定隐藏导入
            '--hidden-import=machine_code_verifier',  # 包含授权模块
            '--hidden-import=startup_optimizer',  # 包含启动优化模块
            '--hidden-import=time_validator',  # 包含时间验证模块
            '--hidden-import=cryptography',  # 包含加密库
            '--hidden-import=cryptography.fernet',  # 包含Fernet加密
            '--exclude-module=matplotlib',  # 排除不需要的模块
            '--exclude-module=numpy',  # 排除不需要的模块
            '--exclude-module=scipy',  # 排除不需要的模块
            '--exclude-module=pandas',  # 排除不需要的模块
            '--exclude-module=PIL',  # 排除不需要的模块
            '--exclude-module=tkinter',  # 排除不需要的模块
            '--optimize=2',  # 启用字节码优化
            'main.py'
        ]
        subprocess.run(command, check=True)
        print("项目打包成功！")
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    package_project()