# 机器码授权系统使用说明

## 概述

这是一个基于机器硬件唯一标识的软件授权系统，通过检测机器的硬件信息生成唯一机器码，只有预设在代码中的机器码才能运行软件。

## 文件说明

1. **machine_code_verifier.py** - 主要的授权验证模块
2. **machine_code_detector.py** - 独立的机器码检测工具
3. **integration_example.py** - 集成示例代码
4. **README_机器码授权系统.md** - 本说明文档

## 工作原理

### 机器码生成
系统通过以下硬件信息生成唯一机器码：
- **Windows系统**：主板序列号、CPU序列号、硬盘序列号
- **Linux/Mac系统**：机器ID、CPU序列号
- **通用信息**：MAC地址、系统架构、处理器信息

所有信息组合后通过SHA-256哈希算法生成32位唯一标识。

### 授权机制
- 在 `machine_code_verifier.py` 中预设授权机器码列表
- 程序启动时检测当前机器码是否在授权列表中
- 如果授权通过，正常启动程序
- 如果未授权，显示授权对话框并阻止程序运行

## 使用步骤

### 1. 获取目标机器的机器码

运行机器码检测工具：
```bash
python machine_code_detector.py
```

工具会显示：
- 当前机器的唯一机器码
- 详细的硬件信息
- 使用说明

点击"复制机器码"按钮将机器码复制到剪贴板。

### 2. 添加机器码到授权列表

编辑 `machine_code_verifier.py` 文件，找到 `authorized_machines` 列表：

```python
self.authorized_machines = [
    "MACHINE_CODE_PLACEHOLDER_1",  # 替换为实际的机器码
    "MACHINE_CODE_PLACEHOLDER_2",  # 替换为实际的机器码
    "MACHINE_CODE_PLACEHOLDER_3",  # 替换为实际的机器码
    # 可以添加更多机器码
]
```

将占位符替换为实际的机器码：

```python
self.authorized_machines = [
    "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",  # 机器A的机器码
    "b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7",  # 机器B的机器码
    "c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8",  # 机器C的机器码
    # 可以继续添加更多授权机器
]
```

### 3. 在主程序中集成授权检查

在你的主程序入口处添加授权检查：

```python
from machine_code_verifier import run_application_with_authorization_check

def main_application():
    # 你的主应用程序代码
    from ui_main_window import Ui_MainWindow
    from PySide6.QtWidgets import QApplication, QMainWindow

    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(window)
    window.show()

    return app.exec()

if __name__ == "__main__":
    # 使用授权检查启动应用
    run_application_with_authorization_check(main_application, "YourAppName")
```

### 4. 打包和分发

使用 PyInstaller 或其他打包工具打包程序：

```bash
pyinstaller --onefile --windowed your_main_program.py
```

打包后的程序只能在授权的机器上运行。

## 安全特性

1. **硬件绑定**：基于多种硬件信息生成机器码，难以伪造
2. **哈希加密**：使用SHA-256算法，不可逆向
3. **多重验证**：结合多种硬件标识，提高唯一性
4. **代码固化**：授权列表直接编译到程序中，无外部配置文件

## 注意事项

1. **硬件变更**：如果用户更换主要硬件（主板、CPU、硬盘），机器码会发生变化，需要重新授权
2. **虚拟机**：在虚拟机中运行可能产生不同的机器码
3. **备份授权**：建议保存每台机器的机器码记录，便于后续管理
4. **测试验证**：在目标机器上测试确保机器码检测正常工作

## 故障排除

### 问题：程序显示"未授权的机器"
**解决方案**：
1. 运行 `machine_code_detector.py` 获取当前机器码
2. 检查机器码是否已添加到 `authorized_machines` 列表中
3. 确保机器码完全匹配（区分大小写）

### 问题：机器码检测失败
**解决方案**：
1. 确保有足够的系统权限运行检测命令
2. 在Windows上确保WMI服务正常运行
3. 检查防病毒软件是否阻止了系统信息访问

### 问题：相同机器产生不同机器码
**解决方案**：
1. 检查硬件是否发生变化
2. 确保系统环境一致
3. 重启后重新检测机器码

## 扩展功能

可以根据需要添加以下功能：
1. **时间限制**：为授权添加有效期
2. **使用次数限制**：限制软件使用次数
3. **网络验证**：结合在线验证服务
4. **日志记录**：记录授权检查日志
5. **加密通信**：与授权服务器加密通信

## 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. PySide6是否正确安装
3. 系统权限是否足够
4. 硬件信息是否可正常获取
