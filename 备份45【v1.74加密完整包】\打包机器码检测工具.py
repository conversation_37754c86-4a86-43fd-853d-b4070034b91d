#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码检测工具打包脚本
"""

import subprocess
import os
import sys

def package_machine_code_detector():
    """打包机器码检测工具为exe"""
    try:
        print("开始打包机器码检测工具...")
        print("=" * 50)
        
        # 检查必要文件是否存在
        if not os.path.exists("machine_code_detector.py"):
            print("❌ 错误：未找到 machine_code_detector.py 文件")
            return False
        
        # 执行 PyInstaller 命令
        command = [
            'pyinstaller',
            '--onefile',                    # 打包为单个exe文件
            '--windowed',                   # 不显示控制台窗口
            '--name=机器码检测工具',          # 指定输出文件名
            '--icon=icon.ico',              # 图标文件（如果存在）
            '--distpath=dist_detector',     # 指定输出目录
            '--workpath=build_detector',    # 指定临时工作目录
            '--specpath=.',                 # spec文件保存位置
            'machine_code_detector.py'      # 源文件
        ]
        
        # 如果没有图标文件，移除图标参数
        if not os.path.exists("icon.ico"):
            command.remove('--icon=icon.ico')
            print("⚠️  警告：未找到 icon.ico 文件，将使用默认图标")
        
        print("执行打包命令:")
        print(" ".join(command))
        print("-" * 50)
        
        # 执行打包
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        
        print("✅ 打包成功！")
        print("=" * 50)
        print("输出信息:")
        if result.stdout:
            print(result.stdout)
        
        # 检查输出文件
        exe_path = os.path.join("dist_detector", "机器码检测工具.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 输出文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            print("🎉 机器码检测工具打包完成！")
        else:
            print("⚠️  警告：未找到输出的exe文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print("=" * 50)
        print("错误信息:")
        if e.stderr:
            print(e.stderr)
        if e.stdout:
            print(e.stdout)
        return False
        
    except FileNotFoundError:
        print("❌ 错误：未找到 PyInstaller")
        print("请先安装 PyInstaller:")
        print("pip install pyinstaller")
        return False
        
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    import shutil
    
    try:
        # 清理临时文件
        if os.path.exists("build_detector"):
            shutil.rmtree("build_detector")
            print("🧹 已清理临时构建文件")
        
        # 清理spec文件
        spec_file = "机器码检测工具.spec"
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print("🧹 已清理spec文件")
            
    except Exception as e:
        print(f"⚠️  清理文件时出错: {e}")

def main():
    """主函数"""
    print("🔧 机器码检测工具打包程序")
    print("=" * 50)
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print("-" * 50)
    
    # 执行打包
    success = package_machine_code_detector()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ 打包完成！")
        print("📁 输出目录: dist_detector/")
        print("🚀 可以运行 机器码检测工具.exe 进行测试")
        
        # 询问是否清理构建文件
        try:
            choice = input("\n是否清理临时构建文件？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                clean_build_files()
        except KeyboardInterrupt:
            print("\n用户取消操作")
    else:
        print("\n" + "=" * 50)
        print("❌ 打包失败！")
        print("请检查错误信息并重试")
    
    print("\n按回车键退出...")
    try:
        input()
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
