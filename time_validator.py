#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间验证模块
实现授权时间限制和防篡改功能
"""

import json
import time
import hashlib
import hmac
import random
import os
import sys
# 不再需要datetime导入，仅使用time模块
try:
    from cryptography.fernet import Fernet
    import base64
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 不再使用网络时间验证

class TimeValidator:
    def __init__(self):
        # 延迟初始化，只设置基本属性
        self.secret_key = "MFChen_Time_Validator_2024_Secret"
        self.max_time_delta = 30 * 60  # 30分钟
        self.max_history_days = 7  # 历史记录有效期

        # 延迟初始化的属性
        self._time_servers = None
        self._cipher_key = None
        self._time_record_file = None

    def _lazy_init(self):
        """延迟初始化耗时的属性"""
        if self._time_servers is None:
            self._time_servers = [
                "http://worldtimeapi.org/api/timezone/Asia/Shanghai",
                "https://timeapi.io/api/Time/current/zone?timeZone=Asia/Shanghai",
                "http://quan.suning.com/getSysTime.do",
                "https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp"
            ]

        if self._cipher_key is None:
            self._cipher_key = self._generate_cipher_key()

        if self._time_record_file is None:
            self._time_record_file = os.path.join(os.path.expanduser("~"), ".mfchen_time_record.dat")

    @property
    def time_servers(self):
        self._lazy_init()
        return self._time_servers

    @property
    def cipher_key(self):
        self._lazy_init()
        return self._cipher_key

    @property
    def time_record_file(self):
        self._lazy_init()
        return self._time_record_file
        
    def _generate_cipher_key(self):
        """生成加密密钥"""
        if CRYPTO_AVAILABLE:
            key_hash = hashlib.sha256(self.secret_key.encode()).digest()
            return base64.urlsafe_b64encode(key_hash)
        else:
            return self.secret_key.encode()

    def _encrypt_data(self, data):
        """加密数据"""
        try:
            json_data = json.dumps(data)
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                encrypted = cipher.encrypt(json_data.encode())
                return encrypted
            else:
                # 简单的XOR加密作为备用
                key = self.cipher_key
                encrypted = bytearray()
                for i, byte in enumerate(json_data.encode()):
                    encrypted.append(byte ^ key[i % len(key)])
                return bytes(encrypted)
        except Exception:
            return None

    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                decrypted = cipher.decrypt(encrypted_data)
                return json.loads(decrypted.decode())
            else:
                # 简单的XOR解密作为备用
                key = self.cipher_key
                decrypted = bytearray()
                for i, byte in enumerate(encrypted_data):
                    decrypted.append(byte ^ key[i % len(key)])
                return json.loads(decrypted.decode())
        except Exception:
            return None
    
    def _generate_hmac(self, net_time, local_time, delta):
        """生成HMAC校验值"""
        message = f"{net_time}{local_time}{delta}"
        return hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()[:16]
    
    def _check_requests_available(self):
        """检查requests库是否可用 - 已禁用"""
        # 不再使用网络时间验证
        return False

    def _get_network_time(self):
        """获取网络时间 - 已禁用，仅使用本地时间"""
        # 不再使用网络时间验证，直接返回None
        return None
    

    
    def _load_time_history(self):
        """加载时间历史记录"""
        if not os.path.exists(self.time_record_file):
            return {"history": [], "first_use": None, "last_use": None}
        
        try:
            with open(self.time_record_file, 'rb') as f:
                encrypted_data = f.read()
            return self._decrypt_data(encrypted_data) or {"history": [], "first_use": None, "last_use": None}
        except Exception:
            return {"history": [], "first_use": None, "last_use": None}
    
    def _save_time_history(self, history_data):
        """保存时间历史记录"""
        try:
            encrypted_data = self._encrypt_data(history_data)
            if encrypted_data:
                with open(self.time_record_file, 'wb') as f:
                    f.write(encrypted_data)
                return True
        except Exception:
            pass
        return False
    
    def _validate_time_record(self, record):
        """验证时间记录的完整性（旧版本兼容）"""
        try:
            if 'net' in record and 'delta' in record:
                # 旧版本网络时间记录
                expected_hash = self._generate_hmac(record['net'], record['local'], record['delta'])
                return record.get('hash') == expected_hash
            else:
                # 新版本本地时间记录
                return self._validate_time_record_local(record)
        except Exception:
            return False

    def _validate_time_record_local(self, record):
        """验证本地时间记录的完整性"""
        try:
            expected_hash = self._generate_hmac(record['local'], record['local'], 0)
            return record.get('hash') == expected_hash
        except Exception:
            return False
    
    def get_current_time_info(self):
        """获取当前时间信息 - 纯本地时间验证"""
        local_time = int(time.time())

        history_data = self._load_time_history()

        # 生成当前时间记录的哈希值
        hash_value = self._generate_hmac(local_time, local_time, 0)

        # 添加新的时间记录
        new_record = {
            "local": local_time,
            "hash": hash_value,
            "timestamp": local_time
        }

        # 初始化历史记录列表
        if "history" not in history_data:
            history_data["history"] = []

        # 添加新记录
        history_data["history"].append(new_record)

        # 只保留最近10条记录（用于检测时间篡改）
        if len(history_data["history"]) > 10:
            history_data["history"] = history_data["history"][-10:]

        # 更新最后使用时间
        history_data["last_use"] = local_time

        # 保存历史记录
        self._save_time_history(history_data)

        return {
            "current_time": local_time,
            "is_network": False,
            "local_time": local_time,
            "delta": 0,
            "history": history_data["history"]
        }



    def validate_authorization_time(self, expire_timestamp):
        """验证授权时间 - 纯本地时间验证"""
        time_info = self.get_current_time_info()
        current_time = time_info["current_time"]
        local_time = time_info["local_time"]
        history_records = time_info.get("history", [])

        # 检查是否为首次使用
        if len(history_records) <= 1:
            # 首次使用，记录首次使用时间
            history_data = self._load_time_history()
            history_data["first_use"] = local_time
            self._save_time_history(history_data)

            # 首次使用时检查授权是否过期
            if local_time > expire_timestamp:
                return self._generate_error_code("A")
            return True, "首次使用授权验证通过（本地时间验证）"

        # 检查时间回滚（12小时检测）
        # 获取最近的有效时间记录（排除当前记录）
        valid_previous_times = []
        for record in history_records[:-1]:  # 排除最后一个（当前）记录
            if self._validate_time_record_local(record):
                valid_previous_times.append(record["local"])

        if valid_previous_times:
            # 获取最近的有效时间
            latest_previous_time = max(valid_previous_times)

            # 检查是否向前调整了12小时以上
            time_diff = latest_previous_time - local_time
            if time_diff > 12 * 3600:  # 12小时 = 12 * 3600秒
                return self._generate_error_code("A")

            # 检查是否有异常的时间跳跃（向后超过24小时可能是正常的，但需要记录）
            if local_time - latest_previous_time > 30 * 24 * 3600:  # 30天
                # 时间向后跳跃超过30天，可能是系统时间异常
                return self._generate_error_code("A")

        # 检查授权是否过期
        if current_time > expire_timestamp:
            return self._generate_error_code("A")

        return True, "授权时间验证通过（本地时间验证）"

    def _generate_error_code(self, error_type):
        """生成错误代码"""
        random_num = random.randint(100, 999)
        error_code = f"{random_num}{error_type}"
        return False, f"授权异常 {error_code}，请重新授权"

# 测试函数
def test_time_validator():
    """测试时间验证器"""
    validator = TimeValidator()

    print("测试时间验证器...")
    time_info = validator.get_current_time_info()
    print(f"当前时间信息: {time_info}")

    # 测试未来时间（应该通过）
    future_time = int(time.time()) + 30 * 24 * 3600  # 30天后
    result = validator.validate_authorization_time(future_time)
    print(f"未来时间验证: {result}")

    # 测试过去时间（应该失败）
    past_time = int(time.time()) - 24 * 3600  # 1天前
    result = validator.validate_authorization_time(past_time)
    print(f"过去时间验证: {result}")

if __name__ == "__main__":
    test_time_validator()
