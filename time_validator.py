#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间验证模块
实现授权时间限制和防篡改功能
"""

import json
import time
import hashlib
import hmac
import random
import os
import sys
from datetime import datetime, timedelta
try:
    from cryptography.fernet import Fernet
    import base64
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 延迟导入requests，避免启动时加载
REQUESTS_AVAILABLE = None

class TimeValidator:
    def __init__(self):
        # 延迟初始化，只设置基本属性
        self.secret_key = "MFChen_Time_Validator_2024_Secret"
        self.max_time_delta = 30 * 60  # 30分钟
        self.max_history_days = 7  # 历史记录有效期

        # 延迟初始化的属性
        self._time_servers = None
        self._cipher_key = None
        self._time_record_file = None

    def _lazy_init(self):
        """延迟初始化耗时的属性"""
        if self._time_servers is None:
            self._time_servers = [
                "http://worldtimeapi.org/api/timezone/Asia/Shanghai",
                "https://timeapi.io/api/Time/current/zone?timeZone=Asia/Shanghai",
                "http://quan.suning.com/getSysTime.do",
                "https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp"
            ]

        if self._cipher_key is None:
            self._cipher_key = self._generate_cipher_key()

        if self._time_record_file is None:
            self._time_record_file = os.path.join(os.path.expanduser("~"), ".mfchen_time_record.dat")

    @property
    def time_servers(self):
        self._lazy_init()
        return self._time_servers

    @property
    def cipher_key(self):
        self._lazy_init()
        return self._cipher_key

    @property
    def time_record_file(self):
        self._lazy_init()
        return self._time_record_file
        
    def _generate_cipher_key(self):
        """生成加密密钥"""
        if CRYPTO_AVAILABLE:
            key_hash = hashlib.sha256(self.secret_key.encode()).digest()
            return base64.urlsafe_b64encode(key_hash)
        else:
            return self.secret_key.encode()

    def _encrypt_data(self, data):
        """加密数据"""
        try:
            json_data = json.dumps(data)
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                encrypted = cipher.encrypt(json_data.encode())
                return encrypted
            else:
                # 简单的XOR加密作为备用
                key = self.cipher_key
                encrypted = bytearray()
                for i, byte in enumerate(json_data.encode()):
                    encrypted.append(byte ^ key[i % len(key)])
                return bytes(encrypted)
        except Exception:
            return None

    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            if CRYPTO_AVAILABLE:
                cipher = Fernet(self.cipher_key)
                decrypted = cipher.decrypt(encrypted_data)
                return json.loads(decrypted.decode())
            else:
                # 简单的XOR解密作为备用
                key = self.cipher_key
                decrypted = bytearray()
                for i, byte in enumerate(encrypted_data):
                    decrypted.append(byte ^ key[i % len(key)])
                return json.loads(decrypted.decode())
        except Exception:
            return None
    
    def _generate_hmac(self, net_time, local_time, delta):
        """生成HMAC校验值"""
        message = f"{net_time}{local_time}{delta}"
        return hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()[:16]
    
    def _check_requests_available(self):
        """检查requests是否可用"""
        global REQUESTS_AVAILABLE
        if REQUESTS_AVAILABLE is None:
            try:
                import requests
                REQUESTS_AVAILABLE = True
            except ImportError:
                REQUESTS_AVAILABLE = False
        return REQUESTS_AVAILABLE

    def _get_network_time(self):
        """获取网络时间"""
        if not self._check_requests_available():
            return None

        for server in self.time_servers:
            try:
                if sys.platform.startswith('win'):
                    # Windows下使用subprocess避免CMD窗口
                    import subprocess
                    result = subprocess.run([
                        'python', '-c',
                        f'import requests; r=requests.get("{server}", timeout=2); print(r.text)'
                    ], capture_output=True, text=True, timeout=5, creationflags=CREATE_NO_WINDOW)

                    if result.returncode == 0:
                        response_text = result.stdout.strip()
                        return self._parse_time_response(server, response_text)
                else:
                    # 非Windows系统直接使用requests
                    import requests
                    response = requests.get(server, timeout=2)
                    if response.status_code == 200:
                        return self._parse_time_response(server, response_text)
            except Exception:
                continue
        return None
    
    def _parse_time_response(self, server, response_text):
        """解析时间响应"""
        try:
            if "worldtimeapi.org" in server:
                data = json.loads(response_text)
                return int(datetime.fromisoformat(data['datetime'].replace('Z', '+00:00')).timestamp())
            elif "timeapi.io" in server:
                data = json.loads(response_text)
                return int(datetime.fromisoformat(data['dateTime']).timestamp())
            elif "suning.com" in server:
                data = json.loads(response_text)
                return int(data['sysTime2']) // 1000
            elif "taobao.com" in server:
                data = json.loads(response_text)
                return int(data['data']['t']) // 1000
        except Exception:
            pass
        return None
    
    def _load_time_history(self):
        """加载时间历史记录"""
        if not os.path.exists(self.time_record_file):
            return {"history": [], "first_use": None, "last_use": None}
        
        try:
            with open(self.time_record_file, 'rb') as f:
                encrypted_data = f.read()
            return self._decrypt_data(encrypted_data) or {"history": [], "first_use": None, "last_use": None}
        except Exception:
            return {"history": [], "first_use": None, "last_use": None}
    
    def _save_time_history(self, history_data):
        """保存时间历史记录"""
        try:
            encrypted_data = self._encrypt_data(history_data)
            if encrypted_data:
                with open(self.time_record_file, 'wb') as f:
                    f.write(encrypted_data)
                return True
        except Exception:
            pass
        return False
    
    def _validate_time_record(self, record):
        """验证时间记录的完整性"""
        try:
            expected_hash = self._generate_hmac(record['net'], record['local'], record['delta'])
            return record.get('hash') == expected_hash
        except Exception:
            return False
    
    def get_current_time_info(self):
        """获取当前时间信息"""
        local_time = int(time.time())
        net_time = self._get_network_time()

        history_data = self._load_time_history()

        # 更新最后使用时间（但不在这里保存，由调用者决定）
        history_data["last_use"] = local_time
        
        if net_time:
            # 有网络时间
            delta = local_time - net_time
            hash_value = self._generate_hmac(net_time, local_time, delta)
            
            # 添加新记录
            new_record = {
                "net": net_time,
                "local": local_time,
                "delta": delta,
                "hash": hash_value
            }
            
            history_data["history"].append(new_record)
            
            # 只保留最近3条记录
            if len(history_data["history"]) > 3:
                history_data["history"] = history_data["history"][-3:]
            
            self._save_time_history(history_data)
            
            return {
                "current_time": net_time,
                "is_network": True,
                "local_time": local_time,
                "delta": delta
            }
        else:
            # 无网络，使用历史记录推算
            return self._estimate_time_from_history(history_data, local_time)

    def _estimate_time_from_history(self, history_data, current_local):
        """基于历史记录推算当前时间"""
        valid_records = []

        # 验证历史记录
        for record in history_data["history"]:
            if self._validate_time_record(record):
                # 检查记录是否在有效期内（7天）
                record_age = current_local - record["local"]
                if record_age <= self.max_history_days * 24 * 3600:
                    valid_records.append(record)

        if valid_records:
            # 使用最新的有效记录
            latest_record = valid_records[-1]
            time_passed = current_local - latest_record["local"]
            estimated_net_time = latest_record["net"] + time_passed

            return {
                "current_time": estimated_net_time,
                "is_network": False,
                "local_time": current_local,
                "delta": latest_record["delta"],
                "estimated": True
            }
        else:
            # 无有效历史记录
            return {
                "current_time": current_local,
                "is_network": False,
                "local_time": current_local,
                "delta": 0,
                "no_valid_history": True
            }

    def validate_authorization_time(self, expire_timestamp):
        """验证授权时间"""
        time_info = self.get_current_time_info()
        # 不要重复加载，使用get_current_time_info中已经更新的数据
        history_data = self._load_time_history()

        current_time = time_info["current_time"]
        local_time = time_info["local_time"]

        # 检查时间回滚
        if history_data["last_use"] and local_time < history_data["last_use"] - 24 * 3600:
            return self._generate_error_code("B")

        # 检查本地时间篡改
        if time_info.get("delta", 0) > self.max_time_delta:
            return self._generate_error_code("B")

        # 检查是否有有效时间记录
        if time_info.get("no_valid_history"):
            # 检查是否为首次使用
            if history_data["first_use"] is None:
                # 真正的首次使用，允许通过但记录首次使用时间
                history_data["first_use"] = local_time
                history_data["last_use"] = local_time
                self._save_time_history(history_data)
                # 首次使用使用本地时间验证
                if local_time > expire_timestamp:
                    return self._generate_error_code("A")
                return True, "首次使用授权验证通过（请确保网络连接以获得完整保护）"
            else:
                # 非首次使用但无有效历史记录
                days_since_first = (local_time - history_data["first_use"]) / (24 * 3600)
                if days_since_first > 7:
                    return self._generate_error_code("A")
                # 网络问题导致的无历史记录，但在7天内允许通过
                if local_time > expire_timestamp:
                    return self._generate_error_code("A")
                return True, "授权验证通过（网络连接异常，使用本地时间验证）"

        # 检查授权是否过期
        if current_time > expire_timestamp:
            return self._generate_error_code("A")

        # 保存历史数据
        self._save_time_history(history_data)

        return True, "授权时间验证通过"

    def _generate_error_code(self, error_type):
        """生成错误代码"""
        random_num = random.randint(100, 999)
        error_code = f"{random_num}{error_type}"
        return False, f"授权异常 {error_code}，请重新授权"

# 测试函数
def test_time_validator():
    """测试时间验证器"""
    validator = TimeValidator()

    print("测试时间验证器...")
    time_info = validator.get_current_time_info()
    print(f"当前时间信息: {time_info}")

    # 测试未来时间（应该通过）
    future_time = int(time.time()) + 30 * 24 * 3600  # 30天后
    result = validator.validate_authorization_time(future_time)
    print(f"未来时间验证: {result}")

    # 测试过去时间（应该失败）
    past_time = int(time.time()) - 24 * 3600  # 1天前
    result = validator.validate_authorization_time(past_time)
    print(f"过去时间验证: {result}")

if __name__ == "__main__":
    test_time_validator()
