# PySide6项目机器码授权系统集成指南

## 🎯 专门针对你的视频混剪工具项目

### 📁 文件清单

已为你的PySide6项目准备的文件：
- ✅ `machine_code_verifier.py` - 主要授权验证模块（已适配PySide6）
- ✅ `machine_code_detector.py` - 机器码检测工具（已适配PySide6）
- ✅ `test_authorization.py` - 测试脚本（已适配PySide6）
- ✅ `main_with_authorization.py` - 集成示例（专为你的项目定制）
- ✅ `get_machine_code.bat` - 快速获取机器码的批处理（已适配PySide6）

### 🚀 快速集成步骤

#### 步骤1：获取当前机器的机器码

运行以下任一方式：
```bash
# 方式1：直接运行Python脚本
python machine_code_detector.py

# 方式2：运行批处理文件（会自动检查和安装PySide6）
get_machine_code.bat

# 方式3：运行简单测试
python test_machine_code.py
```

#### 步骤2：添加机器码到授权列表

编辑 `machine_code_verifier.py` 文件第16-22行：

```python
# 预设的授权机器码列表 - 在打包前需要替换这些占位符
self.authorized_machines = [
    "b837e25899a1d51becdd9fd0bb39bec6",  # 当前测试机器的机器码
    "your_target_machine_code_1",        # 替换为目标机器1的机器码
    "your_target_machine_code_2",        # 替换为目标机器2的机器码
    "your_target_machine_code_3",        # 替换为目标机器3的机器码
    # 可以添加更多机器码
]
```

#### 步骤3：修改你的main.py

有两种集成方式：

**方式A：最小修改（推荐）**

在你现有的 `main.py` 文件开头添加授权检查：

```python
# 在main.py的最开头添加
from machine_code_verifier import check_authorization_only

# 在主程序启动前检查授权
def check_authorization():
    valid, message, machine_id = check_authorization_only("MFChen视频混剪工具")
    if not valid:
        print("=" * 50)
        print("软件授权验证失败")
        print("=" * 50)
        print(message)
        print("=" * 50)
        input("按回车键退出...")
        sys.exit(1)
    print("✅ 授权验证通过，启动程序...")

# 在 if __name__ == "__main__": 之后立即调用
if __name__ == "__main__":
    check_authorization()  # 添加这一行
    
    # 你原来的代码继续...
    app = QApplication(sys.argv)
    # ... 其他代码
```

**方式B：完整GUI集成**

使用提供的 `main_with_authorization.py` 作为模板，将你的main.py逻辑迁移过去。

#### 步骤4：测试授权系统

运行测试确保一切正常：

```bash
python test_authorization.py
```

应该看到：
```
✅ 授权验证成功！可以正常使用软件。
🎉 所有测试通过！授权系统工作正常。
```

### 🔧 针对你项目的特殊配置

#### 保持原有功能不变

授权系统设计为：
- ✅ 不影响你现有的任何功能
- ✅ 不修改你的UI界面
- ✅ 不影响视频处理逻辑
- ✅ 只在程序启动时进行一次检查

#### 与你的设置系统兼容

授权系统独立于你的 `QSettings`，不会冲突：
```python
# 你的设置系统继续正常工作
self.settings = QSettings("YourCompany", "YourAppName")
```

#### 与打包系统兼容

你的现有打包配置无需修改：
```python
# 你的 打包用package.py 和 main.spec 继续有效
# 只需要确保 machine_code_verifier.py 被包含
```

### 📋 部署清单

在分发软件前：

1. **收集目标机器码**
   - 在每台目标机器上运行 `machine_code_detector.py`
   - 记录每台机器的机器码

2. **更新授权列表**
   - 将所有目标机器码添加到 `authorized_machines` 列表
   - 删除测试用的机器码（如果不需要）

3. **最终测试**
   - 在授权机器上测试程序正常启动
   - 在未授权机器上测试程序被正确阻止

4. **打包分发**
   - 使用你现有的打包流程
   - 确保 `machine_code_verifier.py` 被包含在打包中

### ⚠️ 重要注意事项

1. **机器码稳定性**
   - 机器码基于硬件信息，更换主要硬件会导致机器码变化
   - 建议在部署前在目标机器上充分测试

2. **备份授权信息**
   - 保存每台机器的机器码记录
   - 建议建立机器码-用户对应表

3. **虚拟机环境**
   - 虚拟机的机器码可能不稳定
   - 如需支持虚拟机，建议单独测试

### 🆘 故障排除

**问题：显示"未授权的机器"**
```bash
# 解决方案：
1. 运行 python machine_code_detector.py 获取当前机器码
2. 检查机器码是否在 authorized_machines 列表中
3. 确保机器码完全匹配（注意大小写）
```

**问题：PySide6相关错误**
```bash
# 解决方案：
pip install PySide6
# 或者运行 get_machine_code.bat 自动安装
```

**问题：程序无法启动**
```bash
# 解决方案：
1. 检查 machine_code_verifier.py 是否在正确位置
2. 检查导入路径是否正确
3. 运行 python test_authorization.py 进行诊断
```

### 📞 技术支持

如果遇到问题：
1. 首先运行 `test_authorization.py` 进行自诊断
2. 检查控制台输出的错误信息
3. 确认PySide6环境正常
4. 验证机器码生成是否稳定

---

**总结：** 这个授权系统已经完全适配你的PySide6项目，可以无缝集成到你的视频混剪工具中，提供强大的机器码绑定保护。
