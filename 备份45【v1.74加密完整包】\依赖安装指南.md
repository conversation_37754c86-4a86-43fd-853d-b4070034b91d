# 📦 依赖安装指南

## 🔧 必需依赖

### 核心依赖包
```bash
pip install PySide6>=6.5.0
pip install cryptography>=3.4.8
pip install requests>=2.25.1
```

### 一键安装
```bash
pip install -r requirements.txt
```

## 📋 依赖说明

### PySide6
- **用途**: GUI界面框架
- **版本**: 6.5.0+
- **必需**: 是
- **说明**: 主界面和所有UI组件

### cryptography
- **用途**: 时间记录文件加密
- **版本**: 3.4.8+
- **必需**: 是
- **说明**: 保护时间验证数据，防止篡改

### requests
- **用途**: 网络时间同步
- **版本**: 2.25.1+
- **必需**: 是
- **说明**: 从时间服务器获取准确时间

## 🚀 打包配置

### PyInstaller配置
已在打包脚本中包含所有必需的隐藏导入：

```python
'--hidden-import=cryptography',
'--hidden-import=cryptography.fernet',
'--hidden-import=time_validator',
'--hidden-import=machine_code_verifier',
'--hidden-import=startup_optimizer',
```

### 排除的模块
为减小打包体积，排除了以下模块：
- matplotlib
- numpy
- scipy
- pandas
- PIL
- tkinter

## 🔍 依赖检查

### 检查是否正确安装
```python
# 测试PySide6
try:
    from PySide6.QtWidgets import QApplication
    print("✅ PySide6 正常")
except ImportError:
    print("❌ PySide6 未安装")

# 测试cryptography
try:
    from cryptography.fernet import Fernet
    print("✅ cryptography 正常")
except ImportError:
    print("❌ cryptography 未安装")

# 测试requests
try:
    import requests
    print("✅ requests 正常")
except ImportError:
    print("❌ requests 未安装")
```

## 🛠️ 常见问题

### 问题1: cryptography安装失败
**症状**: `pip install cryptography` 失败
**解决方案**:
```bash
# Windows
pip install --upgrade pip
pip install cryptography

# 如果仍然失败，尝试预编译版本
pip install --only-binary=cryptography cryptography
```

### 问题2: PySide6版本冲突
**症状**: 界面显示异常或启动失败
**解决方案**:
```bash
pip uninstall PySide6
pip install PySide6>=6.5.0
```

### 问题3: 打包后缺少模块
**症状**: exe运行时报"ModuleNotFoundError"
**解决方案**: 检查main.spec中的hiddenimports配置

## 📊 环境要求

### Python版本
- **推荐**: Python 3.8+
- **最低**: Python 3.7
- **测试**: Python 3.12

### 操作系统
- **Windows**: 10/11 (推荐)
- **Linux**: Ubuntu 18.04+
- **macOS**: 10.14+

### 硬件要求
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低100MB可用空间
- **网络**: 用于时间同步（可选）

## 🎯 开发环境设置

### 1. 克隆项目
```bash
git clone <项目地址>
cd <项目目录>
```

### 2. 创建虚拟环境（推荐）
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 运行测试
```bash
python main.py
```

### 5. 打包
```bash
python 打包用package.py
```

## ✅ 验证安装

运行以下命令验证所有依赖是否正确安装：

```bash
python -c "
try:
    from PySide6.QtWidgets import QApplication
    from cryptography.fernet import Fernet
    import requests
    print('🎉 所有依赖安装成功！')
except ImportError as e:
    print(f'❌ 依赖缺失: {e}')
"
```

## 📞 技术支持

如果遇到依赖安装问题：

1. **检查Python版本**: `python --version`
2. **更新pip**: `pip install --upgrade pip`
3. **清理缓存**: `pip cache purge`
4. **重新安装**: `pip install --force-reinstall <包名>`

---

**注意**: 确保在打包前所有依赖都已正确安装并测试通过。
