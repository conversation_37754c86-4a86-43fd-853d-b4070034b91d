#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成示例：如何在你的主程序中使用机器码授权系统
"""

import sys
from machine_code_verifier import run_application_with_authorization_check

def main_application():
    """
    你的主应用程序入口函数
    这里放置你的实际应用程序代码
    """
    # 导入你的主窗口或应用程序
    # 例如：
    # from ui_main_window import Ui_MainWindow
    # from PySide6.QtWidgets import QApplication, QMainWindow

    print("主应用程序启动成功！")
    print("机器授权验证通过，可以正常使用软件。")

    # 这里是你的实际应用程序代码
    # 例如：
    # app = QApplication.instance()
    # if app is None:
    #     app = QApplication(sys.argv)
    #
    # window = QMainWindow()
    # ui = Ui_MainWindow()
    # ui.setupUi(window)
    # window.show()
    #
    # return app.exec()

if __name__ == "__main__":
    # 使用机器码授权检查来启动应用程序
    # 如果机器码不在授权列表中，会显示授权对话框并阻止程序运行
    # 如果机器码在授权列表中，会正常启动主应用程序
    
    run_application_with_authorization_check(main_application, "YourAppName")
