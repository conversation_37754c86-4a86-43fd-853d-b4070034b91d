# 🔐 MD5验证系统使用指南

## 🎯 系统升级说明

你的机器码授权系统已经升级为更安全的MD5验证模式：

### ✅ 已解决的问题

1. **✅ CMD闪烁问题已解决**
   - 所有subprocess调用都添加了`CREATE_NO_WINDOW`参数
   - 打包后运行不会再出现CMD窗口闪烁

2. **✅ 验证复杂度大幅提升**
   - 不再直接使用机器码验证
   - 机器码通过复杂算法转换为MD5值
   - 增加了自定义盐值和多重加密步骤

### 🔧 新增工具

#### 1. 机器码转换工具 (`machine_code_converter.py`)
- **功能**：将机器码转换为独特的MD5值
- **特点**：支持单个和批量转换
- **安全性**：使用自定义算法增加破解难度

#### 2. 升级后的验证系统 (`machine_code_verifier.py`)
- **验证方式**：基于MD5值而非直接机器码
- **显示信息**：同时显示机器码和MD5值
- **兼容性**：完全兼容PySide6

## 🚀 使用流程

### 步骤1：获取机器码

在目标机器上运行：
```bash
python machine_code_detector.py
```

获得机器码，例如：`b837e25899a1d51becdd9fd0bb39bec6`

### 步骤2：转换为MD5值

运行机器码转换工具：
```bash
python machine_code_converter.py
```

在GUI界面中：
1. 输入机器码：`b837e25899a1d51becdd9fd0bb39bec6`
2. 点击"转换"按钮
3. 获得MD5值：`5A91875FA25FAA2112C106ADF231D077`

### 步骤3：更新授权列表

编辑 `machine_code_verifier.py` 文件第34-41行：

```python
# 预设的授权MD5列表 - 在打包前需要替换这些占位符
self.authorized_md5_list = [
    "5A91875FA25FAA2112C106ADF231D077",  # 当前测试机器的MD5值
    "目标机器1的MD5值",                    # 替换为实际的MD5值
    "目标机器2的MD5值",                    # 替换为实际的MD5值
    "目标机器3的MD5值",                    # 替换为实际的MD5值
    # 可以添加更多MD5值
]
```

### 步骤4：测试验证

运行测试确保一切正常：
```bash
python test_authorization.py
```

应该看到：
```
当前机器码: b837e25899a1d51becdd9fd0bb39bec6
当前MD5值: 5A91875FA25FAA2112C106ADF231D077
✅ 授权验证成功！可以正常使用软件。
```

### 步骤5：打包分发

使用现有的打包方式：
```bash
python 打包用package.py
```

## 🔒 安全特性升级

### 原有安全特性
- ✅ 硬件绑定（主板、CPU、硬盘序列号）
- ✅ SHA-256哈希加密
- ✅ 代码固化

### 新增安全特性
- 🆕 **MD5转换层**：机器码不直接用于验证
- 🆕 **自定义算法**：多步骤转换增加破解难度
- 🆕 **盐值加密**：使用自定义盐值增强安全性
- 🆕 **字符串变换**：反转、插入、重组等操作
- 🆕 **双重哈希**：SHA-256 + MD5双重保护

### 转换算法详解
```
机器码 → 加盐 → 反转 → 插入字符 → 再次加盐 → MD5哈希 → 大写输出
```

## 📋 批量处理示例

### 批量转换多个机器码

在转换工具中输入：
```
b837e25899a1d51becdd9fd0bb39bec6
a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8
```

获得结果：
```
authorized_md5_list = [
    "5A91875FA25FAA2112C106ADF231D077",
    "另一个MD5值",
    "第三个MD5值",
]
```

直接复制到代码中即可。

## 🆘 故障排除

### 问题：CMD窗口仍然闪烁
**解决方案：**
- 确保使用最新版本的文件
- 重新打包程序

### 问题：MD5验证失败
**解决方案：**
1. 检查机器码是否正确
2. 使用转换工具重新生成MD5
3. 确保MD5值完全匹配（区分大小写）

### 问题：转换工具无法运行
**解决方案：**
```bash
# 确保PySide6已安装
pip install PySide6
```

## 🎉 升级优势

### 安全性提升
- **破解难度**：从直接机器码验证提升到复杂MD5验证
- **隐蔽性**：代码中看不到真实机器码
- **复杂度**：多重加密算法增加逆向难度

### 用户体验改善
- **无闪烁**：彻底解决CMD窗口闪烁问题
- **信息完整**：同时显示机器码和MD5值
- **工具便利**：专门的转换工具简化操作

### 管理便利性
- **批量处理**：支持批量转换多个机器码
- **结果保存**：可保存转换结果到文件
- **格式化输出**：直接生成代码格式

## 📞 技术支持

如果遇到问题：
1. 运行 `python test_authorization.py` 进行自诊断
2. 检查控制台输出的详细信息
3. 确认PySide6环境正常
4. 验证MD5转换是否正确

---

**总结：** 你的授权系统现在具备了更强的安全性和更好的用户体验，可以放心打包分发！
